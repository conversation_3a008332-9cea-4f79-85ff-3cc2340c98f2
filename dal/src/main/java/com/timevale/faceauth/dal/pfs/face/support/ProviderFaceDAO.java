package com.timevale.faceauth.dal.pfs.face.support;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 17
 */
@Repository
public interface ProviderFaceDAO {

  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  @InsertProvider(type = ProviderFaceDAOSqlProvider.class, method = "insert")
  int insert(@Param("e") ProviderFaceDO entity);

  @SelectProvider(type = ProviderFaceDAOSqlProvider.class, method = "getByFaceId")
  ProviderFaceDO getByFaceId(@Param("faceId") String faceId);

  @SelectProvider(type = ProviderFaceDAOSqlProvider.class, method = "getByProviderOrder")
  ProviderFaceDO getByProviderOrder(@Param("provider") String provider, @Param("order") String order);

  @UpdateProvider(type = ProviderFaceDAOSqlProvider.class, method = "completedProviderFace")
  int completedProviderFace(ProviderFaceCompletionDO entity);
}

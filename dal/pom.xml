<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale.faceauth</groupId>
		<artifactId>faceauth-parent</artifactId>
		<version>2.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>faceauth-dal</artifactId>
	<name>faceauth/dal</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.timevale</groupId>
			<artifactId>mandarin-common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.timevale</groupId>
			<artifactId>mandarin-microservice</artifactId>
		</dependency>
        <dependency>
            <groupId>com.timevale.faceauth</groupId>
            <artifactId>faceauth-facade</artifactId>
            <version>${faceauth-facade.version}</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>



	<build>
		<plugins>
			<plugin>
				<groupId>cn.dalgen.plugins</groupId>
				<artifactId>mybatis-maven-plugin</artifactId>
				<version>1.0.0-SNAPSHOT</version>
				<configuration>
					<copyTemplate>false</copyTemplate>
					<outputDirectory>.</outputDirectory>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>

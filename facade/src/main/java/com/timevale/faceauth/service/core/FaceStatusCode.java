package com.timevale.faceauth.service.core;

import com.timevale.mandarin.base.enums.BaseResultCodeEnum;

/**
 * 刷脸业务状态码 int PAAS_FACE_AUTH_BASE_ERRCODE = 1445001;
 *
 * <AUTHOR>
 * @copyright 2019
 * @see com.timevale.mandarin.base.enums.BaseResultCodeEnum;基础错误码
 * @see com.timevale.esign.compontent.common.base.exception.PaasErrorCodeEnum;
 * @date 2019/9/25 12
 */
public enum FaceStatusCode {

  // 通用错误 10000000-10000021
  // 刷脸专有服务错误 1445000 - 1445499
  // 供应商调用错误 1445500 - 1445699
  // 供应商活体检测错误 1445700- 1445799
  // 供应商证件图像比对错误 1445800- 1445899

  OK(BaseResultCodeEnum.SUCCESS.getNCode(), "成功"),
  ERR_INNER(BaseResultCodeEnum.SYSTEM_ERROR.getNCode(), "系统内部错误"),
  NULL_ARGUMENT(BaseResultCodeEnum.NULL_ARGUMENT.getNCode(), "参数为空"),
  ILLEGAL_ARGUMENT(BaseResultCodeEnum.ILLEGAL_ARGUMENT.getNCode(), "非法参数"),

  // 刷脸专有服务错误 1445000 - 1445499
  SERVICE_UNSUPPORTED(1445000, "服务不支持"),
  SERVICE_UNDEFINED(1445001, "服务未定义"),
  UNCOMPLETED_AUTHORIZATION(1445010, "刷脸认证未完成"),
  FAIL_AUTHORIZATION(1445011, "认证失败"),
  FACE_UNSUPPORTED_INITIALIZE_TYPE(1445012, "人脸识别方式不支持"),
  FACE_NOT_FOUND_AUTHORIZATION(1445013, "刷脸认证业务不存在"),
  FACE_UNSUPPORTED_CLIENT_APPLICATION_TYPE(1445014, "不支持的客户段应用类型"),
  FACE_FAILED_AUTHORIZATION_INITIALIZE(1445015, "刷脸认证初始化失败"),
  FACE_FIELD_COMPLETED_INVOCATION(1445016, "刷脸认证完成处理失败"),
  FACE_FAILED_RETURN_INVOCATION(1445017, "刷脸认证跳转失败"),
  FACE_FAILED_CALLBACK_INVOCATION(1445018, "刷脸认证回调失败"),
  FACE_BROWSER_NOT_SUPPORT_VIDEO_RECORD(1445019, "当前浏览器不支持请切换其他方式"),
  FACE_TASK_EXPIRED(1445020, "刷脸任务过期"),
  FACE_PROVIDER_CONFIG_LOAD_FAIL(1445021, "刷脸供应商配置加载失败"),

  RESOURCE_FAILED_DATASOURCE(1445400, "数据源操作错误"),
  RESOURCE_FAIL_PERSISTENCE(1445401, "资源持久化失败"),
  RESOURCE_FAIL_UPLOAD(1445402, "资源上传失败"),
  RESOURCE_FAIL_DOWNLOAD(1445403, "资源下载失败"),
  RESOURCE_ERROR_HTTP_INITIALIZATION(1445404, "HTTP初始化失败"),
  RESOURCE_ERROR_ENCODE_URL(1445405, "URI编码错误"),
  RESOURCE_ERROR_DECODE_URL(1445406, "URI解码错误"),
  RESOURCE_CAMERA_PERMISSION_NOT_ENABLE(1445407, "请授权摄像头权限"),
  RESOURCE_EXCEPTION_INTERRUPT(1445408, "异常中断请重新进入"),
  RESOURCE_NETWORK_EXCEPTION(1445409, "网络不给力,请稍后再试"),
  RESOURCE_VIDEO_NOT_REAL_TIME(1445410, "刷脸视频非实时录制"),

  // 供应商调用错误 1445500 - 1445599
  PROVIDER_FAILED_API(1445500, "供应商调用失败"),
  PROVIDER_NOT_FEE(1445501, "供应商费用不足"),
  PROVIDER_TIME_OUT(1445502, "供应商调用超时"),
  PROVIDER_ERROR_SIGNATURE(1445503, "供应商签名失败"),
  PROVIDER_INVALID_PARAMETER(1445504, "无效的供应商参数"),
  PROVIDER_ERROR_API_RESPONSE(1445505, "供应商接口响应错误"),
  PROVIDER_ERROR_API_RESULT_SYNTAX(1445506, "供应商接口结果错误"),
  PROVIDER_FAILED_CACHED_TICKET(1445507, "供应商登陆凭证缓存失败"),
  PROVIDER_ERROR_CALLBACK_RESPONSE(1445508, "供应商回调数据错误"),

  PROVIDER_USER_IS_NOT_CERTIFY(1445510, "未完成支付宝账号认证"),
  PROVIDER_REQUEST_TOO_MUCH(1445511, "供应商请求频率过高"),
  PROVIDER_DAY_TIMES_LIMIT(1445512, "当日用户验证次数超限"),
  PROVIDER_VIDEO_NOT_ALWAYS(1445513, "上送视频非实时录制"),
  PROVIDER_REQUEST_IS_NOT_EXIST(1445514, "供应商记录不存在"),
  PROVIDER_INVALID_CERT_AGE(1445515, "用户年龄过低，无法使用本产品"),
  PROVIDER_UP_KEEP(1445516, "供应商系统维护中"),
  PROVIDER_RISK_LIMIT(1445517, "触发风控限制"),
  PROVIDER_AUTH_FAIL(1445518, "供应商授权失效"),
  PROVIDER_INVALID_CERT_AGE_14(1445519, "未满14周岁的用户无法使用本产品"),

  // 供应商活体检测错误 1445800- 1445899
  LIVENESS_FAIL_RECOGNITION(1445800, "活体检测未通过"),
  LIVENESS_VIDEO_FORMAT_FAIL(1445801, "视频格式有误或视频大小不合适"),
  LIVENESS_MAYBE_NOT_PERSON(1445802, "疑似非真人录制"),
  LIVENESS_BRIGHTNESS_CHECK_NOT_PASS(1445803, "周围光照太强或太弱"),
  LIVENESS_RESOLUTION_CHECK_NOT_PASS(1445804, "视频像素太低"),
  LIVENESS_INTEGRITY_CHECK_NOT_PASS(1445805, "人脸不完整，请保持人脸清晰无遮挡"),
  LIVENESS_MOUTH_OPEN_NOT_FOUND(1445806, "未检测到张嘴动作，请保证清晰完整人脸完成动作"),
  LIVENESS_EYE_BLINK_NOT_FOUND(1445807, "未检测到眨眼动作，请保证清晰完整人脸完成动作"),
  LIVENESS_FIRST_ACTION_NOT_FOUND(1445808, "未检测到第一个动作"),
  LIVENESS_VIDEO_TOO_LONG(1445809, "视频录制时间过长"),
  LIVENESS_VIDEO_TOO_SHORT(1445810, "视频录制时间过短"),
  LIVENESS_CAN_NOT_DETECT_FACE(1445811, "请保持本人操作且正脸对框"),
  LIVENESS_VOICE_TOO_SMILE(1445812, "视频里的声音太小"),
  LIVENESS_EYE_IS_MASKED(1445813, "眼睛被遮挡，请检测眼镜或周围光线"),

  // 供应商证件图像比对错误 1445900- 1445999
  IDENTITY_PHOTO_CHECK_FAIL(1445900, "无法判断为同一人，请确认身份后重试"),
  IDENTITY_PHOTO_TIME_OUT(1445901, "人脸比对超时"),
  IDENTITY_PHOTO_NAME_IDNO_ERROR(1445902, "姓名或身份证号有误"),
  IDENTITY_PHOTO_SIZE_NOT_STANDARD(1445903, "传入图片过大或过小"),
  IDENTITY_PHOTO_SOURCE_NOT_FOUND(1445904, "库中无此号，请到户籍所在地进行核实"),
  IDENTITY_PHOTO_PHOTO_NOT_FOUND(1445905, "库中无此用户照片，请到户籍所在地进行核实"),
  IDENTITY_PHOTO_RESOLUTION_NOT_PASS(1445906, "传入图片分辨率太低"),
  IDENTITY_PHOTO_CHECK_SIMILARITY_FAIL(1445907, "比对相似度未达到通过标准"),
  IDENTITY_PHOTO_NOT_FOUND_FACE(1445908, "人脸无法识别"),
  IDENTITY_PHOTO_NAME_ERROR(1445909, "姓名格式有误"),
  IDENTITY_PHOTO_ID_NO_ERROR(1445910, "证件号格式有误"),
  IDENTITY_PHOTO_ID_TYPE_ERROR(1445911, "证件类型不支持"),
  ;
  private final int code;
  private final String msg;

  FaceStatusCode(int code, String msg) {
    this.code = code;
    this.msg = msg;
  }

  public int getCode() {
    return code;
  }

  public String getMsg() {
    return msg;
  }

  public FaceException createException(String message) {
    return FaceException.valueOf(this, message);
  }

  public void throwException() {
    throw FaceException.valueOf(this);
  }

  public void throwException(String message) {
    throw FaceException.valueOf(this, message);
  }
}

package com.timevale.faceauth.service.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 *     <p>发起视频识别入参
 */
@Data
public class VideoAuthInput extends BaseInput {


  @NotBlank(message = "应用appId")
  private String appId;

  /** 用户身份姓名 */
  @NotBlank(message = "姓名不能为空")
  private String name;

  /** 用户身份证号 */
  @NotBlank(message = "身份证不能为空")
  private String idNo;

  @NotBlank(message = "视频")
  private String base64Video;



}

package com.timevale.faceauth.service.enums;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/** 认证状态，枚举值如 下： 值为1，认证初始化成 功 值为2，认证链接获取 成功 值为3，认证通过 */
@Slf4j
public enum FaceAuthDbStatusEnum {
  INTI(1),
  URL_RECEIVED(2),
  PASSED(3),
  FAILED(4);

  public Integer value;

  FaceAuthDbStatusEnum(Integer value) {
    this.value = value;
  }

  private static Map<Integer, FaceAuthDbStatusEnum> statusEnumMap;

  public static FaceAuthDbStatusEnum valueOf(Integer value){
    if(null == value){
      log.warn("Not found property["+ value +"] on type["+ FaceAuthDbStatusEnum.class +"] .");
      return null;
    }
    if(null == statusEnumMap){
      synchronized (FaceAuthDbStatusEnum.class){
        if(null == statusEnumMap){
          FaceAuthDbStatusEnum[] enums = values();
          Map<Integer, FaceAuthDbStatusEnum> map = (new HashMap<>(2 * enums.length));
          for(FaceAuthDbStatusEnum statusEnum : enums){
            map.put(statusEnum.value, statusEnum);
          }
          statusEnumMap = map;
        }
      }
    }

    FaceAuthDbStatusEnum status = statusEnumMap.get(value);
    if(null == status){
      log.warn("Not found property["+ value +"] on type["+ FaceAuthDbStatusEnum.class +"] .");
    }
    return status;
  }

  public static boolean isPassed(FaceAuthDbStatusEnum status){
    return PASSED.equals(status);
  }

  public static boolean isCompleted(FaceAuthDbStatusEnum status){
    return (PASSED.equals(status) || FAILED.equals(status));
  }
}

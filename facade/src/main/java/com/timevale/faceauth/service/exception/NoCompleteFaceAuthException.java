package com.timevale.faceauth.service.exception;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.mandarin.base.enums.EnumBase;
import com.timevale.mandarin.base.exception.BaseRuntimeException;

/**
 * 未完成异常
 *
 * <AUTHOR>
 */
public class NoCompleteFaceAuthException extends FaceException {

  public NoCompleteFaceAuthException(int code, String msg) {
    super(code, msg);
  }

  public NoCompleteFaceAuthException(FaceStatusCode status) {
    super(status);
  }

  public NoCompleteFaceAuthException(FaceStatusCode status, String msg) {
    super(status, msg);
  }
}

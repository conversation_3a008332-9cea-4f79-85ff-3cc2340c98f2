package com.timevale.faceauth.service.enums;

/**
 * <AUTHOR> on 2018/7/30
 *     <p>人脸识别 业务类型
 */
public enum FaceAuthBizTypeEnum {

  /** 实名认证 */
  REAL_NAME_AUTH(1, "实名认证"),
  /** 意愿认证 */
  WILL_AUTH(2, "意愿认证"),
  ;

  private Integer type;
  private String des;

  FaceAuthBizTypeEnum(Integer type, String des) {
    this.type = type;
    this.des = des;
  }

  public static FaceAuthBizTypeEnum from(int type) {
    for (FaceAuthBizTypeEnum bizTypeEnum : FaceAuthBizTypeEnum.values()) {
      if (bizTypeEnum.getType() == type) {
        return bizTypeEnum;
      }
    }
    return null;
  }

  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }

  public String getDes() {
    return des;
  }

  public void setDes(String des) {
    this.des = des;
  }
}

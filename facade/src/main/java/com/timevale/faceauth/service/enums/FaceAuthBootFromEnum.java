package com.timevale.faceauth.service.enums;

/**
 * <AUTHOR> on 2018/7/24
 *     <p>人脸识别h5启动来源枚举
 */
public enum FaceAuthBootFromEnum {

  /** 浏览器启动刷脸 */
  BROWSER("browser", "浏览器启动刷脸"),
  /** APP启动刷脸 */
  APP("app", "APP启动刷脸"),

  /** 支付宝 */
  ALIPAY("alipay", "支付宝");

  private String from;
  private String des;

  FaceAuthBootFromEnum(String from, String des) {
    this.from = from;
    this.des = des;
  }

  public static FaceAuthBootFromEnum from(String type) {
    for (FaceAuthBootFromEnum bootFromEnum : FaceAuthBootFromEnum.values()) {
      if (bootFromEnum.getFrom().equals(type)) {
        return bootFromEnum;
      }
    }
    return null;
  }

  public String getFrom() {
    return from;
  }

  public void setFrom(String from) {
    this.from = from;
  }

  public String getDes() {
    return des;
  }

  public void setDes(String des) {
    this.des = des;
  }
}

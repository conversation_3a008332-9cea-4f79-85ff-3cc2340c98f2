package com.timevale.faceauth.service.exception;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 第三方服务商异常
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/9/30 14
 */
@Slf4j
public class ProviderException extends FaceException {

  private ProviderErrorEnum pError;



  public ProviderException(FaceStatusCode status, ProviderErrorEnum pError) {
    super(status);
    this.pError = pError;
  }

  public ProviderException(int code, String msg, ProviderErrorEnum pError) {
    this(code, msg);
    this.pError = pError;
  }

  public ProviderException(String msg) {
    super(FaceStatusCode.PROVIDER_FAILED_API, msg);
  }

  public ProviderException(int code, String msg) {
    super(code, msg);
  }

  public ProviderException(FaceStatusCode status) {
    super(status);
  }

  public ProviderException(FaceException e) {
    super(e.getCode(), e.getMsg());
  }

  public ProviderException(FaceStatusCode status, String msg) {
    super(status, msg);
  }

  public static ProviderException valueOf(String msg) {
    return (new ProviderException(FaceStatusCode.PROVIDER_FAILED_API, msg));
  }

  public ProviderErrorEnum getPError() {
    return pError;
  }

  public static ProviderException valueOf(FaceStatusCode statusCode) {
    return (new ProviderException(statusCode.getCode(), statusCode.getMsg()));
  }

  public static ProviderException buildFaceError(
      String faceErrorCode,
      String faceErrorMsg,
      String providerErrorCode,
      String providerErrorMsg) {
    int code = -1;
    try {
      if (StringUtils.isNotBlank(faceErrorCode)) {
        code = Integer.parseInt(faceErrorCode);
      }
    } catch (Exception e) {
      log.error("face code not a int ", e);
    }
    return new ProviderException(
        code, faceErrorMsg, DefaultProviderError.build(providerErrorCode, providerErrorMsg));
  }
}

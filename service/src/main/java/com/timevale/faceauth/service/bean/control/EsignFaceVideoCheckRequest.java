package com.timevale.faceauth.service.bean.control;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @since 2020/2/27 上午11:27
 */
@ApiModel("e签宝刷脸视频核对请求")
@Data
public class EsignFaceVideoCheckRequest extends ToString {

    /** 文件签名 */
    @ApiModelProperty(value = "文件签名")
    @NotBlank(message = "文件签名")
    private String sign;


}

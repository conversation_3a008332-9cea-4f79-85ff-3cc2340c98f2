package com.timevale.faceauth.service.domain;

import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import org.testng.annotations.Test;

public class ConfigurableFaceAuthorizationInitializersTest extends AbstractTest {

  @Test
  public void getConfigurableFaceAuthorizationInitializer() {
    assert null
        != ConfigurableFaceAuthorizationInitializers.getConfigurableFaceAuthorizationInitializer(
            ConfigurableFaceAuthorizationInitializer.TYPE_SYNCHRONIZATION);

    try {
      ConfigurableFaceAuthorizationInitializers.getConfigurableFaceAuthorizationInitializer(
          "NOT_FOUND");
    } catch (FaceException cause) {
      assert cause.getCode() == FaceStatusCode.SERVICE_UNSUPPORTED.getCode();
    }
  }
}

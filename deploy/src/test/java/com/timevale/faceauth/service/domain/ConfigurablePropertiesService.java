package com.timevale.faceauth.service.domain;

import com.timevale.framework.puppeteer.enums.PropertyChangeType;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/16 19
 */
@Component
public class ConfigurablePropertiesService {

  @Autowired private ConfigurableProperties properties;

  public ConfigurableProperties getProperties() {
    return properties;
  }

  public void changeProperty(String propertyName, String newValue) {
    Map<String, ConfigChange> changes = (new HashMap<>(2));
    ConfigChange change =
        (new ConfigChange(null, propertyName, null, newValue, PropertyChangeType.MODIFIED));
    changes.put(propertyName, change);
    ConfigChangeEvent event = (new ConfigChangeEvent(null, changes));
    properties.onPropertyChanged(event);
    assert newValue.equals(properties.getServerHost());
  }

  public void changeServiceHost(String value) {
    final String propertyName = "server.host";
    changeProperty(propertyName, value);
  }
}

package com.timevale.faceauth.service.domain.support.event;

import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandler;
import com.timevale.faceauth.service.domain.event.ProviderFaceAuthorizationCompletedEvent;
import com.timevale.faceauth.service.domain.event.ProviderFaceAuthorizationInitializedEvent;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderServices;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2020-09-14 21:17
 */
public class FaceOptionLogListenerTest extends AbstractTest {

  @Autowired private FaceOptionLogInitializedListener initializedListener;
  @Autowired private FaceOptionLogResultListener resultListener;
  @Autowired private FaceRepository faceRepository;
  @Autowired private ProviderFaceRepository providerFaceRepository;

  @Test
  public void FaceOptionLogInit() {
    ProviderFaceAuthorizationData data = ProviderFaceAuthorizationData.createBuilder().build();
    ProviderFaceAuthorizationInitializedEvent event =
        new ProviderFaceAuthorizationInitializedEvent("ddbc2898f1d94cc5b1d7dac8e9e172c7", data);
    Assert.assertNotNull(event);
    initializedListener.doHandle(event);
  }

  @Ignore
  @Test
  public void FaceOptionLogResult() {
//    String faceId = "ae478ef8e4d64b6d81ed8a1b0eacafb2";
    String faceId = "b7b9baff38f442a28eef0126cda48d4c";
    FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
    ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceId);
    ConfigurableProviderService faceAuthorizationQueryService =
        ConfigurableProviderServices.getProviderService(faceInfo.getProvider());
    ProviderFaceAuthorizationResult result =
        faceAuthorizationQueryService.queryAuthorizeResult(null, faceInfo, providerFaceInfo);

    ProviderFaceAuthorizationCompletedEvent event =
        new ProviderFaceAuthorizationCompletedEvent(
            ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_QUERY, result);
    try{
      resultListener.doHandleEvent(event);
    } catch (Exception e){
      Assert.assertEquals(false, true);
      return;
    }
    Assert.assertEquals(true, true);
  }
}

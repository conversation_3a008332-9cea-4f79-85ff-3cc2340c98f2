package com.timevale.faceauth.service.file;

import cn.esign.open.component.vedio.*;
import cn.esign.open.component.vedio.encoder.Encoder;
import cn.esign.open.component.vedio.encoder.EncoderException;
import cn.esign.open.component.vedio.encoder.EncodingAttributes;
import cn.esign.open.component.vedio.io.AudioAttributes;
import cn.esign.open.component.vedio.io.VideoAttributes;

import java.io.File;

/**
 * https://www.jianshu.com/p/bbdf53d2643b
 * https://www.cnblogs.com/Dreamer-1/p/10394011.html
 * sudo chmod -R 777 ffmpeg_mac
 * <AUTHOR>
 * @since 2020/3/19 下午7:29
 */
public class MovToMp4Test {

  public static void main(String[] args) {
    File source =
        new File("/Users/<USER>/Documents/1584924318384.mp4");
    File target =
        new File("/Users/<USER>/Documents/1584924318384_smale.mp4");

//    ffmpegChange1(source,target);
    ffmpegChangeMac(source, target);
//    MediaUtil.zipVideo(source, target);
  }

  private static boolean ffmpegChange(File file, File fileSave) {
    try {

      VideoAttributes video = new VideoAttributes();
      video.setCodec("mpeg4");
      video.setBitRate("500k");
      video.setFrameRate(new Integer(1800));

      EncodingAttributes attrs = new EncodingAttributes();
      attrs.setFormat("mp4");
      attrs.setVideoAttributes(video);
      Encoder encoder = new Encoder();

      long beginTime = System.currentTimeMillis();
      encoder.encode(file, fileSave, attrs);
      System.out.println("压缩完成花费时间是：" + ((System.currentTimeMillis() - beginTime)) / 1000 + "秒");
      return true;
    } catch (EncoderException e) {
      System.out.println("压缩错误信息是：" + e.getMessage());
      return false;
    }
  }


  //java代码
  private static boolean ffmpegChange1 (File file,File fileSave){
    try{
      AudioAttributes audio = new AudioAttributes();
      audio.setCodec("libmp3lame");
      audio.setBitRate(new Integer(56000));
      audio.setChannels(new Integer(1));
      audio.setSamplingRate(new Integer(22050));

      VideoAttributes video = new VideoAttributes();
      // video.setCodec(myConfig.getFileFFmpegPath());
//            video.setCodec("libx264");
      video.setCodec("mpeg4");
      // video.setCodec("h264");
      video.setBitRate("800k");
      video.setFrameRate(new Integer(15));

      EncodingAttributes attrs = new EncodingAttributes();
      attrs.setFormat("mp4");
      attrs.setAudioAttributes(audio);
      attrs.setVideoAttributes(video);

      Encoder encoder = new Encoder();

      long beginTime = System.currentTimeMillis();
      MultimediaInfo m = encoder.getInfo(file);
      System.out.println(m.getDuration()/1000 + "秒");
      System.out.println("获取时长花费时间是：" + ((System.currentTimeMillis() - beginTime))/1000 + "秒");
      beginTime = System.currentTimeMillis();
//            encoder.encode(source, fileSave, attrs);

      encoder.encode(file, fileSave, attrs);
//            System.out.println("压缩完成...");
      System.out.println("压缩完成花费时间是：" + ((System.currentTimeMillis() - beginTime))/1000 + "秒");
      return true;
    }catch (EncoderException e){
      e.printStackTrace();
      System.out.println("压缩错误信息是1："+e.getMessage());
      return false;
    }
  }


  private static boolean ffmpegChangeMac (File file,File fileSave){
    try{

      VideoAttributes video = new VideoAttributes();
      //video.setCodec("mpeg4");
      //h264 mpeg4
      video.setCodec("mpeg4");
      video.setBitRate("80k");
      video.setFrameRate(new Integer(80));

      EncodingAttributes attrs = new EncodingAttributes();
      attrs.setFormat("mp4");
      attrs.setVideoAttributes(video);

      Encoder encoder = new Encoder();

      long beginTime = System.currentTimeMillis();
//            encoder.encode(source, fileSave, attrs);

      encoder.encode(file, fileSave, attrs);
//            System.out.println("压缩完成...");
      System.out.println("压缩完成花费时间是：" + ((System.currentTimeMillis() - beginTime))/1000 + "秒");
      return true;
    }catch (EncoderException e){
      e.printStackTrace();
      System.out.println("压缩错误信息是："+e.getMessage());
      return false;
    }
  }





}

package com.timevale.faceauth.dal.pfs.support;

import com.timevale.faceauth.dal.pfs.face.support.*;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/16 17
 */
@Component
public class ProviderFaceService {

  @Autowired private ProviderFaceDAO providerFaceDAO;
  @Autowired private ProviderReturnDAO providerReturnDAO;

  public void completedProviderFace(ProviderFaceDO entity) {
    ProviderFaceCompletionDO completionEntity = (new ProviderFaceCompletionDO());
    completionEntity.faceId = entity.faceId;
    completionEntity.isDone = (byte) 0x01;
    completionEntity.doneTime = System.currentTimeMillis();
    assert 0 < providerFaceDAO.completedProviderFace(completionEntity);

    ProviderFaceDO query = providerFaceDAO.getByFaceId(entity.faceId);
    assert null != query;
    assert completionEntity.doneTime == query.doneTime;
    assert completionEntity.isDone == query.isDone;
  }

  public ProviderFaceDO createProviderFaceEntityAndSave(FaceDO faceEntity) {
    ProviderFaceDO entity = (new ProviderFaceDO());
    entity.faceId = faceEntity.faceId;
    entity.provider = faceEntity.provider;
    entity.fullName = "MOCK_PROVIDER";
    entity.faceInput = "{}";
    entity.faceData = "MOCK_DATA";
    entity.orderNo = System.currentTimeMillis() + "";
    entity.isDone = (byte) 0x00;
    entity.doneTime = 0L;
    entity.thirdpartId = "";
    assert 0 < providerFaceDAO.insert(entity);
    assert 0 < entity.id;
    return entity;
  }

  public void createProviderReturnEntityAndSave(FaceDO faceEntity) {
    ProviderReturnDO entity = (new ProviderReturnDO());
    entity.faceId = faceEntity.faceId;
    entity.data = "mock";
    entity.returnType =
        ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_QUERY;
    entity.provider = faceEntity.provider;
    entity.isOk = (byte) 0x01;
    assert 0 < providerReturnDAO.insert(entity);
    assert 0 < entity.id;
  }
}

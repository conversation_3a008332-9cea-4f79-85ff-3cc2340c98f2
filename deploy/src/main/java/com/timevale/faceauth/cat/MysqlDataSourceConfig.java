package com.timevale.faceauth.cat;

import com.alibaba.druid.pool.DruidDataSource;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.service.aspect.MybatisInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(
    basePackages = "com.timevale.faceauth.dal",
    sqlSessionFactoryRef = "mysqlSqlSessionFactory")
public class MysqlDataSourceConfig {

  @Value("${spring.datasource.url}")
  private String url;

  @Value("${spring.datasource.username}")
  private String username;

  @Value("${spring.datasource.password}")
  private String password;

  @Value("${spring.datasource.initialSize : 10}")
  private int initialSize;

  @Value("${spring.datasource.maxActive : 100}")
  private int maxActive;

  @Value("${spring.datasource.minIdle : 10}")
  private int minIdle;

  @Value("${spring.datasource.maxWait : 5000}")
  private long maxWait;

  @Bean(name = "mysqlDataSource")
  public DataSource mysqlDataSource() {
    DruidDataSource dataSource = new DruidDataSource();
    dataSource.setDriverClassName("com.mysql.jdbc.Driver");
    dataSource.setUrl(url);
    dataSource.setUsername(username);
    dataSource.setPassword(password);
    dataSource.setInitialSize(initialSize);
    dataSource.setMaxActive(maxActive);
    dataSource.setMinIdle(minIdle);
    dataSource.setMaxWait(maxWait);

    // 以下设置是要保证连接的可用，因为这里存在一个三层连接嵌套（hbase、phoenix、druid）使用带来的连接是否已经关闭的问题。
    dataSource.setTimeBetweenEvictionRunsMillis(2000);
    dataSource.setMinEvictableIdleTimeMillis(600000);
    dataSource.setMaxEvictableIdleTimeMillis(900000);
    dataSource.setValidationQuery("SELECT 1");
    dataSource.setTestWhileIdle(true);
    dataSource.setTestOnBorrow(false);
    dataSource.setTestOnReturn(false);
    dataSource.setKeepAlive(true);
    dataSource.setAsyncInit(true);
    return dataSource;
  }

  @Bean(name = "mysqlTransactionManager")
  public DataSourceTransactionManager mysqlTransactionManager(DataSource mysqlDataSource) {
    return new DataSourceTransactionManager(mysqlDataSource);
  }

  @Bean(name = "mysqlSqlSessionFactory")
  public SqlSessionFactory mysqlSqlSessionFactory(DataSource mysqlDataSource) throws Exception {
    final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
//    CatMybatisInterceptor catMybatisInterceptor = new CatMybatisInterceptor(url);
//    sessionFactory.setPlugins(new Interceptor[] {catMybatisInterceptor});
    MybatisInterceptor mybatisInterceptorPlugin = new MybatisInterceptor();
    sessionFactory.setPlugins(new Interceptor[] {mybatisInterceptorPlugin});

    sessionFactory.setDataSource(mysqlDataSource);
    SqlSessionFactory factory = sessionFactory.getObject();
    factory.getConfiguration().setMapUnderscoreToCamelCase(true);
    return factory;
  }
}

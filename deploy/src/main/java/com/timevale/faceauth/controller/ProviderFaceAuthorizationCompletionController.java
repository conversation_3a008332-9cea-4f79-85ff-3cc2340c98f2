package com.timevale.faceauth.controller;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandler;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderServices;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationCallbackService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationReturnService;
import com.timevale.faceauth.service.domain.provider.support.ProviderFaceAuthorizationCompletionUtil;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 供应方刷脸认证完成处理控制器
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/9 17
 */
@Slf4j
@ExternalService
public class ProviderFaceAuthorizationCompletionController {

  /** 供应商回调方式入口 */
  @RestMapping(
      path =
          ProviderFaceAuthorizationCompletionUtil.FACE_AUTHORIZATION_CALLBACK_URL
              + ProviderFaceAuthorizationCompletionUtil.ROUTER_DELIMITED_SYMBOL
              + "{"
              + ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_PROVIDER
              + "}"
              + ProviderFaceAuthorizationCompletionUtil.ROUTER_DELIMITED_SYMBOL
              + "{"
              + ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_FACE_ID
              + "}",
      method = {RequestMethod.POST})
  @ResponseBody
  public String onFaceAuthorizationCallback(
      @PathVariable(ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_PROVIDER)
          String provider,
      @PathVariable(ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_FACE_ID) String faceId,
      HttpServletRequest httpServletRequest)
      throws FaceException {
    log.info("Callback request on faceId[" + faceId + "] for provider[" + provider + "] .");
    checkArguments(
        ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_CALLBACK,
        provider,
        faceId);
    ProviderFaceAuthorizationCallbackService callbackService =
        ConfigurableProviderServices.getProviderService(provider);
    return callbackService.faceAuthorizationCallback(faceId, httpServletRequest);
  }

  private void checkArguments(String completedType, String provider, String faceId) {
    if (StringUtils.isEmpty(provider)) {
      String msg =
          "Field completed provider face authorization on '"
              + completedType
              + "', because not found provider '"
              + provider
              + "' .";
      log.warn(msg);
      throw FaceException.valueOf(FaceStatusCode.FACE_FIELD_COMPLETED_INVOCATION, msg);
    }
    if (StringUtils.isEmpty(faceId)) {
      String msg =
          "Field completed provider face authorization on '"
              + completedType
              + "', because not found face '"
              + faceId
              + "' .";
      log.warn(msg);
      throw FaceException.valueOf(FaceStatusCode.FACE_FIELD_COMPLETED_INVOCATION, msg);
    }
  }

  /** 供应商回跳方式入口 */
  @RestMapping(
      path =
          ProviderFaceAuthorizationCompletionUtil.FACE_AUTHORIZATION_RETURN_URL
              + ProviderFaceAuthorizationCompletionUtil.ROUTER_DELIMITED_SYMBOL
              + "{"
              + ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_PROVIDER
              + "}"
              + ProviderFaceAuthorizationCompletionUtil.ROUTER_DELIMITED_SYMBOL
              + "{"
              + ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_FACE_ID
              + "}",
      method = {RequestMethod.GET})
  public void onFaceAuthorizationReturn(
      @PathVariable(ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_PROVIDER)
          String provider,
      @PathVariable(ProviderFaceAuthorizationCompletionUtil.PARAMETER_NAME_FACE_ID) String faceId,
      HttpServletRequest httpServletRequest,
      HttpServletResponse httpServletResponse)
      throws FaceException {
    log.info("Return request on original faceId[" + faceId + "] for provider[" + provider + "] .");
    faceId = faceId.split("&")[0];
    log.info("Return request on real faceId[" + faceId + "] for provider[" + provider + "] .");
    checkArguments(
        ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_RETURN,
        provider,
        faceId);
    ProviderFaceAuthorizationReturnService returnService =
        ConfigurableProviderServices.getProviderService(provider);
    returnService.faceAuthorizationReturned(faceId, httpServletRequest, httpServletResponse);
  }
}

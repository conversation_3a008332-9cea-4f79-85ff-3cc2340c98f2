package com.timevale.faceauth.controller.audioandvideodual;

import com.timevale.faceauth.service.thirdparty.audiovideodual.context.AudioVideoDualResultEnum;
import com.timevale.faceauth.service.thirdparty.audiovideodual.exception.AudioAndVideoDualRuntimeException;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 记录AudioAndVideoDualFaceController请求入参和出参
 *
 * <p>统一转化异常和返回值类型
 */
@Order(1)
@Aspect
@Component
@Slf4j
public class AudioAndVideoDualResultAspect {

  @Pointcut(
          "execution (public * com.timevale.faceauth.controller.audioandvideodual..*.*(..)) "  )
  public void doAspect() {}

  @Around("doAspect()")
  public Object around(ProceedingJoinPoint joinPoint)   {
    long startTime = System.currentTimeMillis();

    Signature signature = joinPoint.getSignature();
    String methodName = joinPoint.getSignature().getName();
    String className = joinPoint.getSignature().getDeclaringType().getSimpleName();
    StringBuilder paramsBuilder = new StringBuilder();
    Object result;
    try {

      Object[] args = joinPoint.getArgs();
      if (signature instanceof MethodSignature && (args != null && args.length > 0)) {
        Method method = ((MethodSignature) signature).getMethod();
        Parameter[] parameters = method.getParameters();

        for (int i = 0; i < parameters.length; i++) {
          if (args[i] != null) {
            paramsBuilder.append(JsonUtils.obj2json(args[i]));
            paramsBuilder.append(",");
          }
        }
      }

      result = joinPoint.proceed();
    } catch (Throwable e) {
      result = result(e);
    }

    long endTime = System.currentTimeMillis();
    long cost = endTime - startTime;

    long threadId = Thread.currentThread().getId();
    log.info(
        "T-{} REST-REQUEST: cost={} ms  {}.{}(..) {}.",
        threadId,
        cost,
        className,
        methodName,
        paramsBuilder.toString());
    log.info("T-{} REST-RESPONSE: {}", threadId, JsonUtils.obj2json(result));



    return result;
  }

  /**
   * 异常返回结果转化
   *
   * @param e
   * @return
   */
  private BaseResult result(Throwable e) {
    if (e instanceof AudioAndVideoDualRuntimeException) {
      AudioAndVideoDualRuntimeException exception = (AudioAndVideoDualRuntimeException) e;
      log.warn("execute failed as {}-{}.", exception.getCode(), exception.getMessage());
      return BaseResult.fail(exception.getCode(), exception.getMessage());
    }
    log.error("execute failed timeout for exception:"+e.getMessage(), e);
    return BaseResult.fail(AudioVideoDualResultEnum.SYSTEM_ERROR);
  }
}

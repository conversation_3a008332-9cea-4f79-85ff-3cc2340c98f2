package com.timevale.faceauth.controller.audioandvideodual;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2021-04-15 16:59
 */
@Data
@ApiModel("微信小程序刷脸发起基本数据对象")
public class DetectAuthInput {

    /** 用户身份姓名 */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /** 用户身份证号 */
    @NotBlank(message = "身份证不能为空")
    private String idNo;

    @NotBlank(message = "认证结束后重定向的回调链接地址。最长长度1024位")
    private String redirectUrl;

}

/*
 *  __  .__                            .__
 *_/  |_|__| _____   _______  _______  |  |   ____
 *\   __\  |/     \_/ __ \  \/ /\__  \ |  | _/ __ \
 * |  | |  |  Y Y  \  ___/\   /  / __ \|  |_\  ___/
 * |__| |__|__|_|  /\___  >\_/  (____  /____/\___  >
 *               \/     \/           \/          \/
 *
 *                   Copyright 2017-2017 Timevale.
 */
package com.timevale.faceauth.deploy;

import com.timevale.faceauth.service.mq.MQManager;
import com.timevale.faceauth.service.utils.task.MdcTaskDecorator;
import com.timevale.framework.puppeteer.spring.annotation.EnablePuppeteerConfig;
import com.timevale.mandarin.microservice.NoDBService;
import com.timevale.mandarin.microservice.util.ApplicationContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 服务启动入口
 *
 * <AUTHOR> Kunpeng
 * @version $Id: Application.java, v 0.1 2017年11月13日 下午2:23:03 LIU Kunpeng Exp $
 */
//@AutoConfiCat
@NoDBService
@EnableFeignClients(
    basePackages = {
      "com.timevale.filesystem.common.service.api",
      "com.timevale.open.platform.service.service",
      "com.timevale.footstone.antforest.common.rpc.api",
      "com.timevale.facedetectalgo.service",
      "com.timevale.infoauth.service.api",
      "com.timevale.mediaauth.facade.api"
    })
@ComponentScan(
    basePackages = {
      "com.timevale.faceauth.service.integration", //
      "com.timevale.faceauth",
      "com.timevale.faceauth.service",
      "com.timevale.faceauth.task",
      "com.timevale.faceauth.swagger",
      "com.timevale.faceauth.controller",
      "com.timevale.mc",
//      "esign.cat.integration",
      "com.timevale.open.platform.service.service",
      "com.timevale.faceauth.cat",
      "com.timevale.facedetectalgo.service"
    },
    includeFilters = @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*XFaceServiceImpl"))
@EnablePuppeteerConfig({
  "application",
  "JSBZ.SOA_PUBLIC",
  "face_audiovideodual",
  "JSBZ.messagecenter"
})
@EnableAsync
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class Application {

  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
      // MQ消费者要在整个项目实例启动完成之后初始化
      (ApplicationContextUtils.getApplicationContext().getBean(MQManager.class))
              .initConsumerFactory();
  }

    /**
     * 刷脸任务线程池
     *
     * @return
     */
    @Bean("faceAsyncExecutor")
    public AsyncTaskExecutor faceAsyncTaskPool() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("Async-Task-Pool");
        executor.setCorePoolSize(Math.max(cpuCores, 4));
        executor.setMaxPoolSize(cpuCores * 5);
        executor.setQueueCapacity(800);
        executor.setKeepAliveSeconds(300);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new MdcTaskDecorator());
        return executor;
    }





}
